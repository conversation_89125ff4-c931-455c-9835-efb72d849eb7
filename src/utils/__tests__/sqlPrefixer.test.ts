import { SQLPrefixer } from '../sqlPrefixer';

describe('SQLPrefixer', () => {
  let sqlPrefixer: SQLPrefixer;

  beforeEach(() => {
    sqlPrefixer = new SQLPrefixer({
      'db1': 'database1',
      'db2': 'database2',
      'test': 'test_db'
    });
  });

  describe('addDatabasePrefix', () => {
    test('should add prefix to SELECT statement', () => {
      const sql = 'SELECT * FROM users WHERE id = 1';
      const result = sqlPrefixer.addDatabasePrefix(sql, 'database1');
      
      expect(result.modified).toBe('SELECT * FROM database1.users WHERE id = 1');
      expect(result.tablesFound).toEqual(['users']);
      expect(result.original).toBe(sql);
    });

    test('should add prefix to INSERT statement', () => {
      const sql = 'INSERT INTO orders (user_id, amount) VALUES (1, 100)';
      const result = sqlPrefixer.addDatabasePrefix(sql, 'database1');
      
      expect(result.modified).toBe('INSERT INTO database1.orders (user_id, amount) VALUES (1, 100)');
      expect(result.tablesFound).toEqual(['orders']);
    });

    test('should add prefix to UPDATE statement', () => {
      const sql = 'UPDATE products SET price = 99 WHERE id = 10';
      const result = sqlPrefixer.addDatabasePrefix(sql, 'database1');
      
      expect(result.modified).toBe('UPDATE database1.products SET price = 99 WHERE id = 10');
      expect(result.tablesFound).toEqual(['products']);
    });

    test('should add prefix to JOIN statement', () => {
      const sql = 'SELECT * FROM users JOIN orders ON users.id = orders.user_id';
      const result = sqlPrefixer.addDatabasePrefix(sql, 'database1');
      
      expect(result.modified).toBe('SELECT * FROM database1.users JOIN database1.orders ON database1.users.id = database1.orders.user_id');
      expect(result.tablesFound).toEqual(['users', 'orders']);
    });

    test('should add prefix to ALTER TABLE statement', () => {
      const sql = 'ALTER TABLE users ADD email VARCHAR(100)';
      const result = sqlPrefixer.addDatabasePrefix(sql, 'database1');
      
      expect(result.modified).toBe('ALTER TABLE database1.users ADD email VARCHAR(100)');
      expect(result.tablesFound).toEqual(['users']);
    });

    test('should add prefix to CREATE TABLE statement', () => {
      const sql = 'CREATE TABLE customers (id INT, name VARCHAR(100))';
      const result = sqlPrefixer.addDatabasePrefix(sql, 'database1');
      
      expect(result.modified).toBe('CREATE TABLE database1.customers (id INT, name VARCHAR(100))');
      expect(result.tablesFound).toEqual(['customers']);
    });

    test('should not modify tables that already have database prefix', () => {
      const sql = 'SELECT * FROM database1.users WHERE id = 1';
      const result = sqlPrefixer.addDatabasePrefix(sql, 'database1');
      
      expect(result.modified).toBe(sql);
      expect(result.tablesFound).toEqual([]);
    });

    test('should handle multiple tables in one statement', () => {
      const sql = 'SELECT * FROM users u JOIN orders o ON u.id = o.user_id JOIN products p ON o.product_id = p.id';
      const result = sqlPrefixer.addDatabasePrefix(sql, 'database1');
      
      expect(result.modified).toBe('SELECT * FROM database1.users u JOIN database1.orders o ON u.id = o.user_id JOIN database1.products p ON o.product_id = p.id');
      expect(result.tablesFound).toEqual(['users', 'orders', 'products']);
    });
  });

  describe('batchProcess', () => {
    test('should process multiple SQL statements', () => {
      const sqlStatements = [
        'SELECT * FROM users WHERE id = 1',
        'INSERT INTO orders (user_id, amount) VALUES (1, 100)',
        'UPDATE products SET price = 99 WHERE id = 10'
      ];
      
      const result = sqlPrefixer.batchProcess(sqlStatements, 'database1');
      
      expect(result.summary.total).toBe(3);
      expect(result.summary.processed).toBe(3);
      expect(result.summary.tablesModified).toBe(3);
      expect(result.results).toHaveLength(3);
      
      expect(result.results[0].modified).toBe('SELECT * FROM database1.users WHERE id = 1');
      expect(result.results[1].modified).toBe('INSERT INTO database1.orders (user_id, amount) VALUES (1, 100)');
      expect(result.results[2].modified).toBe('UPDATE database1.products SET price = 99 WHERE id = 10');
    });
  });

  describe('extractSQLStatements', () => {
    test('should extract SQL statements separated by semicolons', () => {
      const text = 'SELECT * FROM users; INSERT INTO orders VALUES (1, 100); UPDATE products SET price = 99';
      const statements = sqlPrefixer.extractSQLStatements(text);
      
      expect(statements).toEqual([
        'SELECT * FROM users',
        'INSERT INTO orders VALUES (1, 100)',
        'UPDATE products SET price = 99'
      ]);
    });

    test('should handle empty statements', () => {
      const text = 'SELECT * FROM users;; INSERT INTO orders VALUES (1, 100);';
      const statements = sqlPrefixer.extractSQLStatements(text);
      
      expect(statements).toEqual([
        'SELECT * FROM users',
        'INSERT INTO orders VALUES (1, 100)'
      ]);
    });
  });

  describe('validateDatabaseName', () => {
    test('should validate correct database names', () => {
      expect(sqlPrefixer.validateDatabaseName('database1')).toBe(true);
      expect(sqlPrefixer.validateDatabaseName('test_db')).toBe(true);
      expect(sqlPrefixer.validateDatabaseName('_private_db')).toBe(true);
    });

    test('should reject invalid database names', () => {
      expect(sqlPrefixer.validateDatabaseName('1database')).toBe(false);
      expect(sqlPrefixer.validateDatabaseName('data-base')).toBe(false);
      expect(sqlPrefixer.validateDatabaseName('data base')).toBe(false);
      expect(sqlPrefixer.validateDatabaseName('')).toBe(false);
    });
  });

  describe('detectSQLType', () => {
    test('should detect SQL statement types', () => {
      expect(sqlPrefixer.detectSQLType('SELECT * FROM users')).toBe('SELECT');
      expect(sqlPrefixer.detectSQLType('INSERT INTO orders VALUES (1, 100)')).toBe('INSERT');
      expect(sqlPrefixer.detectSQLType('UPDATE products SET price = 99')).toBe('UPDATE');
      expect(sqlPrefixer.detectSQLType('DELETE FROM users WHERE id = 1')).toBe('DELETE');
      expect(sqlPrefixer.detectSQLType('ALTER TABLE users ADD column')).toBe('ALTER');
      expect(sqlPrefixer.detectSQLType('CREATE TABLE test (id INT)')).toBe('CREATE');
      expect(sqlPrefixer.detectSQLType('DROP TABLE test')).toBe('DROP');
      expect(sqlPrefixer.detectSQLType('SHOW TABLES')).toBe('UNKNOWN');
    });
  });
});
