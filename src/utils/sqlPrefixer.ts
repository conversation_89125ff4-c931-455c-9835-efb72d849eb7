import { DatabaseConfig, SQLPrefixResult, BatchSQLResult } from '../types';

/**
 * 为SQL语句中的表名添加数据库前缀
 * 基于PHP版本的逻辑转换为TypeScript
 */
export class SQLPrefixer {
  private databases: DatabaseConfig;

  constructor(databases: DatabaseConfig = {}) {
    this.databases = databases;
  }

  /**
   * 设置数据库配置
   */
  setDatabases(databases: DatabaseConfig): void {
    this.databases = databases;
  }

  /**
   * 获取数据库配置
   */
  getDatabases(): DatabaseConfig {
    return this.databases;
  }

  /**
   * 为单个SQL语句添加数据库前缀
   */
  addDatabasePrefix(sql: string, dbName: string): SQLPrefixResult {
    // 正则表达式匹配表名，覆盖 SELECT, INSERT, UPDATE, JOIN 和 ALTER TABLE 语句
    // 匹配不带数据库前缀的表名，假设表名是标准标识符
    const pattern = /\b(FROM|JOIN|INTO|UPDATE|ALTER\s+TABLE|CREATE\s+TABLE)\s+([a-zA-Z_][a-zA-Z0-9_]*)\b/gi;
    
    const tablesFound: string[] = [];
    
    // 替换回调函数
    const modifiedSql = sql.replace(pattern, (match, keyword, tableName) => {
      // 检查表名是否已经有数据库前缀
      if (!tableName.includes('.')) {
        tablesFound.push(tableName);
        return `${keyword} ${dbName}.${tableName}`;
      }
      return match;
    });

    return {
      original: sql,
      modified: modifiedSql,
      tablesFound: [...new Set(tablesFound)] // 去重
    };
  }

  /**
   * 批量处理多个SQL语句
   */
  batchProcess(sqlStatements: string[], dbName: string): BatchSQLResult {
    const results: SQLPrefixResult[] = [];
    let totalTablesModified = 0;

    sqlStatements.forEach(sql => {
      const result = this.addDatabasePrefix(sql.trim(), dbName);
      results.push(result);
      totalTablesModified += result.tablesFound.length;
    });

    return {
      results,
      summary: {
        total: sqlStatements.length,
        processed: results.length,
        tablesModified: totalTablesModified
      }
    };
  }

  /**
   * 从文本中提取SQL语句（按分号分割）
   */
  extractSQLStatements(text: string): string[] {
    return text
      .split(';')
      .map(sql => sql.trim())
      .filter(sql => sql.length > 0);
  }

  /**
   * 验证数据库名称格式
   */
  validateDatabaseName(dbName: string): boolean {
    const dbNamePattern = /^[a-zA-Z_][a-zA-Z0-9_]*$/;
    return dbNamePattern.test(dbName);
  }

  /**
   * 获取预定义的数据库配置示例
   */
  static getDefaultDatabases(): DatabaseConfig {
    return {
      'db1': 'database1',
      'db2': 'database2', 
      'db3': 'database3',
      'prod': 'production_db',
      'test': 'test_db',
      'dev': 'development_db'
    };
  }

  /**
   * 检测SQL语句类型
   */
  detectSQLType(sql: string): string {
    const upperSQL = sql.trim().toUpperCase();
    
    if (upperSQL.startsWith('SELECT')) return 'SELECT';
    if (upperSQL.startsWith('INSERT')) return 'INSERT';
    if (upperSQL.startsWith('UPDATE')) return 'UPDATE';
    if (upperSQL.startsWith('DELETE')) return 'DELETE';
    if (upperSQL.startsWith('ALTER')) return 'ALTER';
    if (upperSQL.startsWith('CREATE')) return 'CREATE';
    if (upperSQL.startsWith('DROP')) return 'DROP';
    
    return 'UNKNOWN';
  }
}

// 导出默认实例
export const sqlPrefixer = new SQLPrefixer();
