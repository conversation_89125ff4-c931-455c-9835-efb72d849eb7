import React from 'react';
import {Code, Database, FileText, Globe, Settings} from 'lucide-react';
import {Tool, ToolTab} from '../types';

interface ToolTabsProps {
    activeTab: ToolTab;
    onTabChange: (tab: ToolTab) => void;
}

const tools: Tool[] = [
    {
        id: 'json-formatter',
        name: 'JSON Formatter',
        description: 'Format and validate JSON data',
        icon: 'FileText'
    },
    {
        id: 'sql-prefixer',
        name: 'SQL前缀工具',
        description: '为SQL语句批量添加数据库前缀',
        icon: 'Database'
    }
];

const iconMap = {
    Database,
    Code,
    Globe,
    FileText,
    Settings
};

export const ToolTabs: React.FC<ToolTabsProps> = ({activeTab, onTabChange}) => {
    return (
        <div className="border-b border-gray-200 bg-white">
            <nav className="flex space-x-8 px-6" aria-label="Tabs">
                {tools.map((tool) => {
                    const Icon = iconMap[tool.icon as keyof typeof iconMap];
                    const isActive = activeTab === tool.id;

                    return (
                        <button
                            key={tool.id}
                            onClick={() => onTabChange(tool.id)}
                            className={`
                flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200
                ${isActive
                                ? 'border-primary-500 text-primary-600'
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                            }
              `}
                            aria-current={isActive ? 'page' : undefined}
                        >
                            <Icon className="w-5 h-5"/>
                            <span>{tool.name}</span>
                        </button>
                    );
                })}
            </nav>
        </div>
    );
};
