import React, { useState } from 'react';
import { AlertCircle, CheckCircle, Copy, FileText, Wand2 } from 'lucide-react';

export const JSONFormatter: React.FC = () => {
  const [jsonInput, setJsonInput] = useState('');
  const [formattedJSON, setFormattedJSON] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isValid, setIsValid] = useState<boolean | null>(null);

  const formatJSON = () => {
    try {
      if (!jsonInput.trim()) {
        setError('JSON input cannot be empty');
        setIsValid(false);
        return;
      }
      
      const parsedJSON = JSON.parse(jsonInput);
      const formatted = JSON.stringify(parsedJSON, null, 2);
      setFormattedJSON(formatted);
      setIsValid(true);
      setError(null);
    } catch (err) {
      setError(`Invalid JSON: ${(err as Error).message}`);
      setIsValid(false);
    }
  };

  const minifyJSON = () => {
    try {
      if (!jsonInput.trim()) {
        setError('JSON input cannot be empty');
        setIsValid(false);
        return;
      }
      
      const parsedJSON = JSON.parse(jsonInput);
      const minified = JSON.stringify(parsedJSON);
      setFormattedJSON(minified);
      setIsValid(true);
      setError(null);
    } catch (err) {
      setError(`Invalid JSON: ${(err as Error).message}`);
      setIsValid(false);
    }
  };

  const validateJSON = () => {
    try {
      if (!jsonInput.trim()) {
        setError('JSON input cannot be empty');
        setIsValid(false);
        return;
      }
      
      JSON.parse(jsonInput);
      setIsValid(true);
      setError(null);
    } catch (err) {
      setError(`Invalid JSON: ${(err as Error).message}`);
      setIsValid(false);
    }
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(formattedJSON || jsonInput);
  };

  return (
    <div className="flex h-full flex-col">
      {/* Toolbar */}
      <div className="p-4 border-b border-gray-200 bg-white">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900">JSON Formatter</h2>
          <div className="flex items-center space-x-2">
            <button
              onClick={formatJSON}
              className="btn btn-secondary text-sm"
              disabled={!jsonInput}
            >
              <Wand2 className="w-4 h-4 mr-1" />
              Format
            </button>
            <button
              onClick={minifyJSON}
              className="btn btn-secondary text-sm"
              disabled={!jsonInput}
            >
              <Wand2 className="w-4 h-4 mr-1" />
              Minify
            </button>
            <button
              onClick={validateJSON}
              className="btn btn-secondary text-sm"
              disabled={!jsonInput}
            >
              <CheckCircle className="w-4 h-4 mr-1" />
              Validate
            </button>
            <button
              onClick={copyToClipboard}
              className="btn btn-primary text-sm"
              disabled={!jsonInput && !formattedJSON}
            >
              <Copy className="w-4 h-4 mr-1" />
              Copy
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 p-4 flex flex-col md:flex-row gap-4">
        {/* Input */}
        <div className="flex-1 flex flex-col">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            JSON Input
          </label>
          <textarea
            value={jsonInput}
            onChange={(e) => setJsonInput(e.target.value)}
            className="textarea flex-1 resize-none font-mono"
            placeholder="Paste your JSON here..."
          />
        </div>

        {/* Output */}
        <div className="flex-1 flex flex-col">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Formatted Output
          </label>
          <textarea
            value={formattedJSON}
            readOnly
            className="textarea flex-1 resize-none font-mono bg-gray-50"
            placeholder="Formatted JSON will appear here..."
          />
        </div>
      </div>

      {/* Validation Results */}
      {isValid !== null && (
        <div className={`mx-4 mb-4 p-3 rounded-lg border ${
          isValid
            ? 'bg-green-50 border-green-200 text-green-700'
            : 'bg-red-50 border-red-200 text-red-700'
        }`}>
          <div className="flex items-center space-x-2">
            {isValid ? (
              <CheckCircle className="w-4 h-4" />
            ) : (
              <AlertCircle className="w-4 h-4" />
            )}
            <span className="font-medium">
              {isValid ? 'Valid JSON' : 'Invalid JSON'}
            </span>
          </div>
          {!isValid && error && (
            <p className="mt-1 text-sm">{error}</p>
          )}
        </div>
      )}
    </div>
  );
};