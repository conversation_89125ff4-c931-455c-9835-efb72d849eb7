import React, { useState, useEffect } from 'react';
import { Database, Copy, Download, Upload, Settings, Play, RotateCcw, Plus, Trash2 } from 'lucide-react';
import { SQLPrefixer } from '../utils/sqlPrefixer';
import { DatabaseConfig, SQLPrefixResult, BatchSQLResult, MultipleDatabaseResult } from '../types';

export const SQLPrefixerComponent: React.FC = () => {
  const [sqlInput, setSqlInput] = useState('');
  const [databasesInput, setDatabasesInput] = useState('');
  const [databases, setDatabases] = useState<DatabaseConfig>(SQLPrefixer.getDefaultDatabases());
  const [results, setResults] = useState<MultipleDatabaseResult | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [processingMode, setProcessingMode] = useState<'single' | 'multiple'>('multiple');

  const sqlPrefixer = new SQLPrefixer(databases);

  // 示例SQL语句
  const exampleSQL = `SELECT * FROM users WHERE id = 1;
INSERT INTO orders (user_id, amount) VALUES (1, 100);
UPDATE products SET price = 99 WHERE id = 10;
SELECT * FROM users JOIN orders ON users.id = orders.user_id;
ALTER TABLE users ADD email VARCHAR(100);
ALTER TABLE products MODIFY COLUMN price DECIMAL(10,2);
CREATE TABLE customers (id INT, name VARCHAR(100));`;

  const handleProcess = async () => {
    if (!sqlInput.trim()) {
      alert('请输入SQL语句');
      return;
    }

    if (!databasesInput.trim()) {
      alert('请输入数据库名列表');
      return;
    }

    setIsProcessing(true);

    try {
      const statements = sqlPrefixer.extractSQLStatements(sqlInput);
      const databaseNames = sqlPrefixer.parseDatabaseNames(databasesInput);

      if (databaseNames.length === 0) {
        alert('未找到有效的数据库名');
        return;
      }

      const multiResult = sqlPrefixer.processMultipleDatabases(statements, databaseNames);
      setResults(multiResult);
    } catch (error) {
      console.error('处理SQL时出错:', error);
      alert('处理SQL时出错，请检查输入格式');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleCopyResult = (dbName?: string) => {
    if (!results) return;

    let resultText = '';

    if (dbName) {
      // 复制单个数据库的结果
      const dbResult = results[dbName];
      if (dbResult) {
        resultText = `-- Database: ${dbName}\n` +
          dbResult.results.map(result => result.modified).join(';\n') + ';';
      }
    } else {
      // 复制所有数据库的结果
      resultText = Object.entries(results)
        .map(([db, dbResult]) =>
          `-- Database: ${db}\n` +
          dbResult.results.map(result => result.modified).join(';\n') + ';\n'
        ).join('\n');
    }

    navigator.clipboard.writeText(resultText).then(() => {
      alert('结果已复制到剪贴板');
    });
  };

  const handleDownloadResult = (dbName?: string) => {
    if (!results) return;

    let resultText = '';
    let filename = 'modified_sql.sql';

    if (dbName) {
      // 下载单个数据库的结果
      const dbResult = results[dbName];
      if (dbResult) {
        resultText = `-- Database: ${dbName}\n` +
          dbResult.results.map(result => result.modified).join(';\n') + ';';
        filename = `${dbName}_modified_sql.sql`;
      }
    } else {
      // 下载所有数据库的结果
      resultText = Object.entries(results)
        .map(([db, dbResult]) =>
          `-- Database: ${db}\n` +
          dbResult.results.map(result => result.modified).join(';\n') + ';\n'
        ).join('\n');
      filename = 'all_databases_modified_sql.sql';
    }

    const blob = new Blob([resultText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleLoadExample = () => {
    setSqlInput(exampleSQL);
  };

  const handleReset = () => {
    setSqlInput('');
    setResults(null);
    setDatabasesInput('');
  };

  const loadExampleDatabases = () => {
    setDatabasesInput('database1\ndatabase2\ndatabase3\ntest_db\nproduction_db');
  };

  const addCustomDatabase = () => {
    const alias = prompt('请输入数据库别名:');
    const name = prompt('请输入数据库名称:');
    
    if (alias && name && sqlPrefixer.validateDatabaseName(name)) {
      const newDatabases = { ...databases, [alias]: name };
      setDatabases(newDatabases);
      sqlPrefixer.setDatabases(newDatabases);
    }
  };

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Database className="w-6 h-6 text-blue-600" />
            <div>
              <h2 className="text-lg font-semibold text-gray-900">SQL数据库前缀工具</h2>
              <p className="text-sm text-gray-500">为SQL语句中的表名批量添加数据库前缀</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowSettings(!showSettings)}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
            >
              <Settings className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>

      <div className="flex-1 flex overflow-hidden">
        {/* Left Panel - Input */}
        <div className="w-1/2 flex flex-col border-r border-gray-200">
          <div className="bg-white border-b border-gray-200 px-4 py-3">
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-gray-900">SQL输入</h3>
              <div className="flex items-center space-x-2">
                <button
                  onClick={handleLoadExample}
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  加载示例
                </button>
                <button
                  onClick={handleReset}
                  className="p-1 text-gray-400 hover:text-gray-600"
                >
                  <RotateCcw className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>

          <div className="flex-1 p-4">
            <textarea
              value={sqlInput}
              onChange={(e) => setSqlInput(e.target.value)}
              placeholder="请输入SQL语句，多条语句用分号分隔..."
              className="w-full h-full resize-none border border-gray-300 rounded-lg p-3 font-mono text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Database Input */}
          <div className="bg-white border-t border-gray-200 p-4">
            <div className="space-y-3">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    数据库名列表
                  </label>
                  <button
                    onClick={loadExampleDatabases}
                    className="text-xs text-blue-600 hover:text-blue-800"
                  >
                    加载示例
                  </button>
                </div>
                <textarea
                  value={databasesInput}
                  onChange={(e) => setDatabasesInput(e.target.value)}
                  placeholder="输入数据库名，支持多种分隔符：&#10;database1&#10;database2&#10;database3&#10;或用逗号、分号、空格分隔"
                  rows={4}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                />
                <div className="text-xs text-gray-500 mt-1">
                  支持换行、逗号、分号、空格等分隔符，自动去重
                </div>
              </div>

              <button
                onClick={handleProcess}
                disabled={isProcessing}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
              >
                <Play className="w-4 h-4" />
                <span>{isProcessing ? '批量处理中...' : '批量处理SQL'}</span>
              </button>
            </div>
          </div>
        </div>

        {/* Right Panel - Results */}
        <div className="w-1/2 flex flex-col">
          <div className="bg-white border-b border-gray-200 px-4 py-3">
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-gray-900">处理结果</h3>
              {results && (
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleCopyResult()}
                    className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                    title="复制所有结果"
                  >
                    <Copy className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleDownloadResult()}
                    className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                    title="下载所有结果"
                  >
                    <Download className="w-4 h-4" />
                  </button>
                </div>
              )}
            </div>
          </div>

          <div className="flex-1 overflow-auto">
            {results ? (
              <div className="p-4 space-y-4">
                {/* Overall Summary */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <h4 className="font-medium text-blue-900 mb-2">总体摘要</h4>
                  <div className="text-sm text-blue-800 space-y-1">
                    <div>数据库数量: {Object.keys(results).length}</div>
                    <div>总处理量: {Object.values(results).reduce((sum, db) => sum + db.summary.total, 0)} 条SQL</div>
                    <div>总修改表数: {Object.values(results).reduce((sum, db) => sum + db.summary.tablesModified, 0)}</div>
                  </div>
                </div>

                {/* Database Results */}
                <div className="space-y-6">
                  {Object.entries(results).map(([dbName, dbResult]) => (
                    <div key={dbName} className="border border-gray-200 rounded-lg overflow-hidden">
                      {/* Database Header */}
                      <div className="bg-gray-50 border-b border-gray-200 px-4 py-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <Database className="w-5 h-5 text-gray-600" />
                            <div>
                              <h3 className="font-medium text-gray-900">{dbName}</h3>
                              <p className="text-sm text-gray-500">
                                {dbResult.summary.processed} 条SQL，{dbResult.summary.tablesModified} 个表被修改
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => handleCopyResult(dbName)}
                              className="p-1 text-gray-400 hover:text-gray-600 rounded"
                              title={`复制 ${dbName} 的结果`}
                            >
                              <Copy className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleDownloadResult(dbName)}
                              className="p-1 text-gray-400 hover:text-gray-600 rounded"
                              title={`下载 ${dbName} 的结果`}
                            >
                              <Download className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      </div>

                      {/* SQL Results */}
                      <div className="p-4 space-y-3">
                        {dbResult.results.map((result, index) => (
                          <div key={index} className="bg-white border border-gray-100 rounded p-3">
                            <div className="text-xs text-gray-500 mb-2">SQL #{index + 1}</div>

                            {result.tablesFound.length > 0 && (
                              <div className="mb-2">
                                <span className="text-xs text-green-600">
                                  修改的表: {result.tablesFound.join(', ')}
                                </span>
                              </div>
                            )}

                            <div className="space-y-2">
                              <div>
                                <div className="text-xs text-gray-500 mb-1">原始:</div>
                                <code className="block text-xs bg-gray-100 p-2 rounded border font-mono">
                                  {result.original}
                                </code>
                              </div>
                              <div>
                                <div className="text-xs text-gray-500 mb-1">修改后:</div>
                                <code className="block text-xs bg-green-50 p-2 rounded border font-mono text-green-800">
                                  {result.modified}
                                </code>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="flex-1 flex items-center justify-center text-gray-500">
                <div className="text-center">
                  <Database className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                  <p>输入SQL语句和数据库名列表，然后点击处理按钮查看结果</p>
                  <p className="text-sm mt-2">支持批量处理多个数据库，结果将按数据库分组显示</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Settings Panel */}
      {showSettings && (
        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96 max-h-96 overflow-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">数据库配置</h3>
              <button
                onClick={() => setShowSettings(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ×
              </button>
            </div>
            
            <div className="space-y-3">
              {Object.entries(databases).map(([alias, name]) => (
                <div key={alias} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <div>
                    <div className="font-medium">{alias}</div>
                    <div className="text-sm text-gray-500">{name}</div>
                  </div>
                </div>
              ))}
              
              <button
                onClick={addCustomDatabase}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700"
              >
                添加数据库
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
