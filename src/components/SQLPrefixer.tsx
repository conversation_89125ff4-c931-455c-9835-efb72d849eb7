import React, { useState, useEffect } from 'react';
import { Database, Copy, Download, Upload, Settings, Play, RotateCcw, Plus, Trash2, ChevronDown, ChevronRight } from 'lucide-react';
import { SQLPrefixer } from '../utils/sqlPrefixer';
import { SQLPrefixResult, BatchSQLResult, MultipleDatabaseResult } from '../types';

export const SQLPrefixerComponent: React.FC = () => {
  const [sqlInput, setSqlInput] = useState('');
  const [databasesInput, setDatabasesInput] = useState('');
  const [results, setResults] = useState<MultipleDatabaseResult | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [expandedDatabases, setExpandedDatabases] = useState<Set<string>>(new Set());
  const [expandAllDatabases, setExpandAllDatabases] = useState(true);

  const sqlPrefixer = new SQLPrefixer();

  // 示例SQL语句
  const exampleSQL = `SELECT * FROM users WHERE id = 1;
INSERT INTO orders (user_id, amount) VALUES (1, 100);
UPDATE products SET price = 99 WHERE id = 10;
SELECT * FROM users JOIN orders ON users.id = orders.user_id;
ALTER TABLE users ADD email VARCHAR(100);
ALTER TABLE products MODIFY COLUMN price DECIMAL(10,2);
CREATE TABLE customers (id INT, name VARCHAR(100));`;

  const handleProcess = async () => {
    if (!sqlInput.trim()) {
      alert('请输入SQL语句');
      return;
    }

    if (!databasesInput.trim()) {
      alert('请输入数据库名列表');
      return;
    }

    setIsProcessing(true);
    
    try {
      const statements = sqlPrefixer.extractSQLStatements(sqlInput);
      const databaseNames = sqlPrefixer.parseDatabaseNames(databasesInput);
      
      if (databaseNames.length === 0) {
        alert('未找到有效的数据库名');
        return;
      }

      const multiResult = sqlPrefixer.processMultipleDatabases(statements, databaseNames);
      setResults(multiResult);
      
      // 默认展开所有数据库
      if (expandAllDatabases) {
        setExpandedDatabases(new Set(databaseNames));
      } else {
        setExpandedDatabases(new Set());
      }
    } catch (error) {
      console.error('处理SQL时出错:', error);
      alert('处理SQL时出错，请检查输入格式');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleCopyResult = (dbName?: string) => {
    if (!results) return;
    
    let resultText = '';
    
    if (dbName) {
      // 复制单个数据库的结果
      const dbResult = results[dbName];
      if (dbResult) {
        resultText = `-- Database: ${dbName}\n` + 
          dbResult.results.map(result => result.modified).join(';\n') + ';';
      }
    } else {
      // 复制所有数据库的结果
      resultText = Object.entries(results)
        .map(([db, dbResult]) => 
          `-- Database: ${db}\n` + 
          dbResult.results.map(result => result.modified).join(';\n') + ';\n'
        ).join('\n');
    }
    
    navigator.clipboard.writeText(resultText).then(() => {
      alert('结果已复制到剪贴板');
    });
  };

  const handleDownloadResult = (dbName?: string) => {
    if (!results) return;
    
    let resultText = '';
    let filename = 'modified_sql.sql';
    
    if (dbName) {
      // 下载单个数据库的结果
      const dbResult = results[dbName];
      if (dbResult) {
        resultText = `-- Database: ${dbName}\n` + 
          dbResult.results.map(result => result.modified).join(';\n') + ';';
        filename = `${dbName}_modified_sql.sql`;
      }
    } else {
      // 下载所有数据库的结果
      resultText = Object.entries(results)
        .map(([db, dbResult]) => 
          `-- Database: ${db}\n` + 
          dbResult.results.map(result => result.modified).join(';\n') + ';\n'
        ).join('\n');
      filename = 'all_databases_modified_sql.sql';
    }
    
    const blob = new Blob([resultText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleLoadExample = () => {
    setSqlInput(exampleSQL);
  };

  const handleReset = () => {
    setSqlInput('');
    setResults(null);
    setDatabasesInput('');
  };

  const loadExampleDatabases = () => {
    const exampleDbs = SQLPrefixer.getDefaultDatabaseNames();
    setDatabasesInput(exampleDbs.join('\n'));
  };

  const toggleDatabaseExpansion = (dbName: string) => {
    const newExpanded = new Set(expandedDatabases);
    if (newExpanded.has(dbName)) {
      newExpanded.delete(dbName);
    } else {
      newExpanded.add(dbName);
    }
    setExpandedDatabases(newExpanded);
  };

  const toggleAllDatabases = () => {
    if (!results) return;
    
    const allDbNames = Object.keys(results);
    if (expandedDatabases.size === allDbNames.length) {
      // 全部展开，则全部折叠
      setExpandedDatabases(new Set());
    } else {
      // 部分或全部折叠，则全部展开
      setExpandedDatabases(new Set(allDbNames));
    }
  };

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Database className="w-6 h-6 text-blue-600" />
            <div>
              <h2 className="text-lg font-semibold text-gray-900">SQL数据库前缀工具</h2>
              <p className="text-sm text-gray-500">为SQL语句中的表名批量添加数据库前缀</p>
            </div>
          </div>
        </div>
      </div>

      <div className="flex-1 flex overflow-hidden">
        {/* Left Panel - Input */}
        <div className="w-1/2 flex flex-col border-r border-gray-200">
          <div className="bg-white border-b border-gray-200 px-4 py-3">
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-gray-900">SQL输入</h3>
              <div className="flex items-center space-x-2">
                <button
                  onClick={handleLoadExample}
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  加载示例
                </button>
                <button
                  onClick={handleReset}
                  className="p-1 text-gray-400 hover:text-gray-600"
                >
                  <RotateCcw className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>

          <div className="flex-1 p-4">
            <textarea
              value={sqlInput}
              onChange={(e) => setSqlInput(e.target.value)}
              placeholder="请输入SQL语句，多条语句用分号分隔..."
              className="w-full h-full resize-none border border-gray-300 rounded-lg p-3 font-mono text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Database Input */}
          <div className="bg-white border-t border-gray-200 p-4">
            <div className="space-y-3">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    数据库名列表
                  </label>
                  <button
                    onClick={loadExampleDatabases}
                    className="text-xs text-blue-600 hover:text-blue-800"
                  >
                    加载示例
                  </button>
                </div>
                <textarea
                  value={databasesInput}
                  onChange={(e) => setDatabasesInput(e.target.value)}
                  placeholder="输入数据库名，支持多种分隔符：&#10;database1&#10;database2&#10;database3&#10;或用逗号、分号、空格分隔"
                  rows={4}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                />
                <div className="text-xs text-gray-500 mt-1">
                  支持换行、逗号、分号、空格等分隔符，自动去重
                </div>
              </div>

              <button
                onClick={handleProcess}
                disabled={isProcessing}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
              >
                <Play className="w-4 h-4" />
                <span>{isProcessing ? '批量处理中...' : '批量处理SQL'}</span>
              </button>
            </div>
          </div>
        </div>

        {/* Right Panel - Results */}
        <div className="w-1/2 flex flex-col">
          <div className="bg-white border-b border-gray-200 px-4 py-3">
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-gray-900">处理结果</h3>
              {results && (
                <div className="flex items-center space-x-2">
                  <button
                    onClick={toggleAllDatabases}
                    className="text-sm text-blue-600 hover:text-blue-800 px-2 py-1 rounded"
                    title={expandedDatabases.size === Object.keys(results).length ? "折叠全部" : "展开全部"}
                  >
                    {expandedDatabases.size === Object.keys(results).length ? "折叠全部" : "展开全部"}
                  </button>
                  <div className="w-px h-4 bg-gray-300"></div>
                  <button
                    onClick={() => handleCopyResult()}
                    className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                    title="复制所有结果"
                  >
                    <Copy className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleDownloadResult()}
                    className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                    title="下载所有结果"
                  >
                    <Download className="w-4 h-4" />
                  </button>
                </div>
              )}
            </div>
          </div>

          <div className="flex-1 overflow-auto">
            {results ? (
              <div className="p-4 space-y-4">
                {/* Overall Summary */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <h4 className="font-medium text-blue-900 mb-2">总体摘要</h4>
                  <div className="text-sm text-blue-800 space-y-1">
                    <div>数据库数量: {Object.keys(results).length}</div>
                    <div>总处理量: {Object.values(results).reduce((sum, db) => sum + db.summary.total, 0)} 条SQL</div>
                    <div>总修改表数: {Object.values(results).reduce((sum, db) => sum + db.summary.tablesModified, 0)}</div>
                  </div>
                </div>

                {/* Database Results */}
                <div className="space-y-4">
                  {Object.entries(results).map(([dbName, dbResult]) => {
                    const isExpanded = expandedDatabases.has(dbName);
                    return (
                      <div key={dbName} className="border border-gray-200 rounded-lg overflow-hidden">
                        {/* Database Header */}
                        <div
                          className="bg-gray-50 border-b border-gray-200 px-4 py-3 cursor-pointer hover:bg-gray-100 transition-colors"
                          onClick={() => toggleDatabaseExpansion(dbName)}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <div className="flex items-center space-x-2">
                                {isExpanded ? (
                                  <ChevronDown className="w-4 h-4 text-gray-500" />
                                ) : (
                                  <ChevronRight className="w-4 h-4 text-gray-500" />
                                )}
                                <Database className="w-5 h-5 text-gray-600" />
                              </div>
                              <div>
                                <h3 className="font-medium text-gray-900 flex items-center space-x-2">
                                  <span>{dbName}</span>
                                  {!isExpanded && (
                                    <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                      {dbResult.summary.processed}条
                                    </span>
                                  )}
                                </h3>
                                <p className="text-sm text-gray-500">
                                  {dbResult.summary.processed} 条SQL，{dbResult.summary.tablesModified} 个表被修改
                                  {!isExpanded && " - 点击展开查看详情"}
                                </p>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2" onClick={(e) => e.stopPropagation()}>
                              <button
                                onClick={() => handleCopyResult(dbName)}
                                className="p-1 text-gray-400 hover:text-gray-600 rounded hover:bg-gray-200"
                                title={`复制 ${dbName} 的结果`}
                              >
                                <Copy className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => handleDownloadResult(dbName)}
                                className="p-1 text-gray-400 hover:text-gray-600 rounded hover:bg-gray-200"
                                title={`下载 ${dbName} 的结果`}
                              >
                                <Download className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        </div>
